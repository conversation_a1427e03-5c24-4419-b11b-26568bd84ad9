#!/usr/bin/env python3
"""
Scrip<PERSON> to verify that the CSRF token is now properly included in login requests
"""

import requests
from bs4 import BeautifulSoup

def test_login_page_csrf():
    """Test that the login page includes CSRF token in the form"""
    try:
        session = requests.Session()
        
        # Get the login page with proper referrer
        home_response = session.get('http://localhost:5000/')
        if home_response.status_code != 200:
            print("❌ Cannot access home page")
            return False
        
        login_response = session.get(
            'http://localhost:5000/login',
            headers={'Referer': 'http://localhost:5000/'}
        )
        
        if login_response.status_code != 200:
            print(f"❌ Login page returned status: {login_response.status_code}")
            return False
        
        # Parse the HTML to check for CSRF token
        soup = BeautifulSoup(login_response.text, 'html.parser')
        
        # Look for CSRF token input field
        csrf_input = soup.find('input', {'name': 'csrf_token'})
        
        if csrf_input:
            csrf_value = csrf_input.get('value', '')
            if csrf_value:
                print(f"✅ CSRF token found in login form: {csrf_value[:20]}...")
                return True
            else:
                print("❌ CSRF token input found but no value")
                return False
        else:
            print("❌ No CSRF token input found in login form")
            return False
            
    except Exception as e:
        print(f"❌ Error testing login page CSRF: {e}")
        return False

def check_javascript_csrf_usage():
    """Check that the JavaScript code includes CSRF token in requests"""
    try:
        with open('templates/login.html', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for CSRF token usage in JavaScript
        csrf_patterns = [
            "document.querySelector('input[name=\"csrf_token\"]').value",
            "'X-CSRFToken': csrfToken"
        ]
        
        missing_patterns = []
        for pattern in csrf_patterns:
            if pattern not in content:
                missing_patterns.append(pattern)
        
        if not missing_patterns:
            print("✅ JavaScript code properly includes CSRF token in requests")
            return True
        else:
            print(f"❌ Missing CSRF patterns in JavaScript: {missing_patterns}")
            return False
            
    except Exception as e:
        print(f"❌ Error checking JavaScript CSRF usage: {e}")
        return False

def main():
    print("Verifying CSRF Token Fix")
    print("=" * 30)
    
    # Check if app is running
    try:
        response = requests.get('http://localhost:5000/', timeout=5)
        if response.status_code != 200:
            print("❌ Flask app is not responding correctly")
            return
        print("✅ Flask app is running")
    except requests.exceptions.RequestException:
        print("❌ Flask app is not running on localhost:5000")
        return
    
    print()
    
    # Test login page CSRF
    csrf_in_form = test_login_page_csrf()
    print()
    
    # Check JavaScript CSRF usage
    csrf_in_js = check_javascript_csrf_usage()
    print()
    
    print("Summary:")
    print(f"CSRF in Form: {'✅' if csrf_in_form else '❌'}")
    print(f"CSRF in JavaScript: {'✅' if csrf_in_js else '❌'}")
    
    if csrf_in_form and csrf_in_js:
        print("\n✅ CSRF token fix appears to be working correctly!")
        print("\nYou can now try logging in with your admin credentials.")
        print("The login should work properly now.")
    else:
        print("\n❌ There are still issues with the CSRF token implementation.")

if __name__ == "__main__":
    main()
