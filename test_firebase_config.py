#!/usr/bin/env python3
"""
Script to test Firebase configuration and authentication flow
"""

import os
import requests
import json
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_firebase_config():
    """Test if the Firebase configuration endpoint is working"""
    try:
        # Test the Flask app's Firebase config endpoint
        response = requests.get('http://localhost:5000/get_firebase_config')
        
        if response.status_code == 200:
            config = response.json()
            print("✅ Firebase config endpoint working")
            print(f"API Key: {config.get('apiKey', 'MISSING')[:10]}...")
            print(f"Auth Domain: {config.get('authDomain', 'MISSING')}")
            print(f"Project ID: {config.get('projectId', 'MISSING')}")
            
            # Check if all required fields are present
            required_fields = ['apiKey', 'authDomain', 'projectId']
            missing_fields = [field for field in required_fields if not config.get(field)]
            
            if missing_fields:
                print(f"❌ Missing Firebase config fields: {missing_fields}")
                return False
            else:
                print("✅ All required Firebase config fields present")
                return True
        else:
            print(f"❌ Firebase config endpoint failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to Flask app. Is it running on localhost:5000?")
        return False
    except Exception as e:
        print(f"❌ Error testing Firebase config: {e}")
        return False

def test_csrf_token():
    """Test if CSRF token endpoint is working"""
    try:
        response = requests.get('http://localhost:5000/get_csrf_token')
        
        if response.status_code == 200:
            data = response.json()
            csrf_token = data.get('csrf_token')
            if csrf_token:
                print("✅ CSRF token endpoint working")
                print(f"CSRF Token: {csrf_token[:20]}...")
                return True
            else:
                print("❌ CSRF token missing from response")
                return False
        else:
            print(f"❌ CSRF token endpoint failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing CSRF token: {e}")
        return False

def check_environment_variables():
    """Check if all required environment variables are set"""
    print("Checking environment variables...")
    
    required_vars = [
        'FIREBASE_API_KEY',
        'FIREBASE_AUTH_DOMAIN', 
        'FIREBASE_PROJECT_ID',
        'ADMIN_UIDS',
        'FLASK_SECRET_KEY'
    ]
    
    missing_vars = []
    for var in required_vars:
        value = os.environ.get(var)
        if value:
            print(f"✅ {var}: {'*' * min(len(value), 10)}")
        else:
            print(f"❌ {var}: MISSING")
            missing_vars.append(var)
    
    if missing_vars:
        print(f"\n❌ Missing environment variables: {missing_vars}")
        return False
    else:
        print("\n✅ All required environment variables present")
        return True

def main():
    print("Testing Firebase Authentication Setup")
    print("=" * 50)
    
    # Check environment variables
    env_ok = check_environment_variables()
    print()
    
    # Test Firebase config
    firebase_ok = test_firebase_config()
    print()
    
    # Test CSRF token
    csrf_ok = test_csrf_token()
    print()
    
    print("Summary:")
    print(f"Environment Variables: {'✅' if env_ok else '❌'}")
    print(f"Firebase Config: {'✅' if firebase_ok else '❌'}")
    print(f"CSRF Token: {'✅' if csrf_ok else '❌'}")
    
    if all([env_ok, firebase_ok, csrf_ok]):
        print("\n✅ All basic checks passed. The issue might be in the frontend JavaScript or login flow.")
        print("\nNext steps:")
        print("1. Check browser developer console for JavaScript errors")
        print("2. Check network tab for failed requests during login")
        print("3. Verify that the login form is submitting correctly")
    else:
        print("\n❌ Some basic checks failed. Fix these issues first.")

if __name__ == "__main__":
    main()
