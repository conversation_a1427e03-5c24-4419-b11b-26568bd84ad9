<!DOCTYPE html>
{% if not request.endpoint %}
  <script>window.location.href = "/";</script>
{% endif %}
{% if not request.endpoint %}
  <script>window.location.href = "/";</script>
{% endif %}
<html lang="he" dir="rtl">
<head>
<meta charset="UTF-8">
<title>Login</title>
<style>
body {
    font-family: sans-serif;
    margin: 20px;
    direction: rtl;
    text-align: right;
}
.auth-container {
    max-width: 400px;
    margin: 0 auto;
    padding: 20px;
}
.auth-section {
    margin-bottom: 30px;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 8px;
}
.auth-divider {
    text-align: center;
    margin: 20px 0;
    position: relative;
}
.auth-divider::before {
    content: "";
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #ddd;
    z-index: -1;
}
.auth-divider span {
    background: white;
    padding: 0 10px;
    color: #666;
}
.form-group {
    margin-bottom: 15px;
}
.form-group label {
    display: block;
    margin-bottom: 5px;
}
.form-group input {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    direction: ltr;
    text-align: left;
}
button {
    width: 100%;
    padding: 10px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
}
#loginBtn {
    background-color: #4285f4;
    color: white;
}
#emailLoginBtn {
    background-color: #28a745;
    color: white;
}
.error-message {
    color: red;
    margin-top: 10px;
    display: none;
}
</style>
</head>
<body>
  <div class="auth-container">
    <h1>Login</h1>
    
    <!-- Email/Password Login Section -->
    <div class="auth-section">
      <h2>התחברות באמצעות אימייל</h2>
      <form id="emailLoginForm">
        {{ csrf_token() }} {# Add CSRF token field #}
        <div class="form-group">
          <label for="email">אימייל:</label>
          <input type="email" id="email" required>
        </div>
        <div class="form-group">
          <label for="password">סיסמה:</label>
          <input type="password" id="password" required>
        </div>
        <button type="submit" id="emailLoginBtn">התחבר</button>
        <p id="emailError" class="error-message"></p>
      </form>
      <div style="text-align: center; margin-top: 15px;">
        <p>אין לך חשבון? <a href="/register">הירשם כאן</a></p>
      </div>
    </div>

    <div class="auth-divider">
      <span>או</span>
    </div>

    <!-- Google Login Section -->
    <div class="auth-section">
      <h2>התחברות באמצעות Google</h2>
      <button id="loginBtn">Login with Google</button>
      <p id="googleError" class="error-message"></p>
    </div>
  </div>

  <!-- Add Firebase SDKs -->
  <script src="https://www.gstatic.com/firebasejs/9.6.1/firebase-app-compat.js"></script>
  <script src="https://www.gstatic.com/firebasejs/9.6.1/firebase-auth-compat.js"></script>

  <script>
    console.log("Login page JavaScript starting...");

    // Fetch Firebase configuration from server
    fetch('/get_firebase_config')
      .then(response => {
        console.log("Firebase config response:", response);
        return response.json();
      })
      .then(firebaseConfig => {
        console.log("Firebase config received:", firebaseConfig);
        // Initialize Firebase
        const app = firebase.initializeApp(firebaseConfig);
        const auth = firebase.auth();
        console.log("Firebase initialized successfully");

    // UI Elements
    const loginBtn = document.getElementById('loginBtn');
    const emailLoginForm = document.getElementById('emailLoginForm');
    const emailInput = document.getElementById('email');
    const passwordInput = document.getElementById('password');
    const emailError = document.getElementById('emailError');
    const googleError = document.getElementById('googleError');

    // Google Sign-In Provider
    const provider = new firebase.auth.GoogleAuthProvider();

    // Email/Password Sign In
    emailLoginForm.addEventListener('submit', (e) => {
      console.log("Form submitted, preventing default...");
      e.preventDefault();
      const email = emailInput.value;
      const password = passwordInput.value;
      console.log("Attempting login with email:", email);

      auth.signInWithEmailAndPassword(email, password)
        .then((userCredential) => {
          const user = userCredential.user;
          console.log("User signed in:", user);

          user.getIdToken().then(idToken => {
            console.log("ID token:", idToken);

            fetch('/set_token', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({ token: idToken }),
            })
            .then(response => {
              if (response.ok) {
                window.location.href = "/";
              } else {
                console.error('Failed to set token:', response.status);
                emailError.textContent = 'Failed to authenticate. Please try again.';
                emailError.style.display = 'block';
              }
            })
            .catch(error => {
              console.error('Error setting token:', error);
              emailError.textContent = 'An error occurred. Please try again.';
              emailError.style.display = 'block';
            });
          });
        })
        .catch((error) => {
          console.error("Error signing in:", error);
          emailError.textContent = error.message;
          emailError.style.display = 'block';
        });
    });

    // Google Sign-in with Popup
    loginBtn.addEventListener('click', () => {
      auth.signInWithPopup(provider)
        .then((result) => {
          if (result.credential) {
            console.log("User signed in:", result.user);

            result.user.getIdToken().then(idToken => {
              console.log("ID token:", idToken);

              fetch('/set_token', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify({ token: idToken }),
              })
              .then(response => {
                if (response.ok) {
                  window.location.href = "/";
                } else {
                  console.error('Failed to set token:', response.status);
                  googleError.textContent = 'Failed to authenticate. Please try again.';
                  googleError.style.display = 'block';
                }
              })
              .catch(error => {
                console.error('Error setting token:', error);
                googleError.textContent = 'An error occurred. Please try again.';
                googleError.style.display = 'block';
              });
            });
          } else {
            console.error("Credential is null. Result:", JSON.stringify(result, null, 2));
            googleError.textContent = 'Authentication failed. Please try again.';
            googleError.style.display = 'block';
          }
        })
        .catch((error) => {
          console.error("Error signing in:", error);
          console.error("Full error object:", JSON.stringify(error, null, 2));
          googleError.textContent = error.message;
          googleError.style.display = 'block';
        });
    });
      })
      .catch(error => {
        console.error('Error fetching Firebase configuration:', error);
        alert('Error loading Firebase configuration: ' + error.message);
      });
  </script>
</body>
</html>