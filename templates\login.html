<!DOCTYPE html>
{% if not request.endpoint %}
  <script>window.location.href = "/";</script>
{% endif %}
{% if not request.endpoint %}
  <script>window.location.href = "/";</script>
{% endif %}
<html lang="he" dir="rtl">
<head>
<meta charset="UTF-8">
<title>Login</title>
<style>
body {
    font-family: sans-serif;
    margin: 20px;
    direction: rtl;
    text-align: right;
}
.auth-container {
    max-width: 400px;
    margin: 0 auto;
    padding: 20px;
}
.auth-section {
    margin-bottom: 30px;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 8px;
}
.auth-divider {
    text-align: center;
    margin: 20px 0;
    position: relative;
}
.auth-divider::before {
    content: "";
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #ddd;
    z-index: -1;
}
.auth-divider span {
    background: white;
    padding: 0 10px;
    color: #666;
}
.form-group {
    margin-bottom: 15px;
}
.form-group label {
    display: block;
    margin-bottom: 5px;
}
.form-group input {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    direction: ltr;
    text-align: left;
}
button {
    width: 100%;
    padding: 10px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
}
#loginBtn {
    background-color: #4285f4;
    color: white;
}
#emailLoginBtn {
    background-color: #28a745;
    color: white;
}
.error-message {
    color: red;
    margin-top: 10px;
    display: none;
}
</style>
</head>
<body>
  <div class="auth-container">
    <h1>Login</h1>
    
    <!-- Email/Password Login Section -->
    <div class="auth-section">
      <h2>התחברות באמצעות אימייל</h2>
      <form id="emailLoginForm" method="post" action="#" onsubmit="return false;">
        {{ csrf_token() }} {# Add CSRF token field #}
        <div class="form-group">
          <label for="email">אימייל:</label>
          <input type="email" id="email" required>
        </div>
        <div class="form-group">
          <label for="password">סיסמה:</label>
          <input type="password" id="password" required>
        </div>
        <button type="submit" id="emailLoginBtn">התחבר</button>
        <p id="emailError" class="error-message"></p>
      </form>
      <div style="text-align: center; margin-top: 15px;">
        <p>אין לך חשבון? <a href="/register">הירשם כאן</a></p>
      </div>
    </div>

    <div class="auth-divider">
      <span>או</span>
    </div>

    <!-- Google Login Section -->
    <div class="auth-section">
      <h2>התחברות באמצעות Google</h2>
      <button id="loginBtn">Login with Google</button>
      <p id="googleError" class="error-message"></p>
    </div>
  </div>

  <!-- Add Firebase SDKs -->
  <script src="https://www.gstatic.com/firebasejs/9.6.1/firebase-app-compat.js"></script>
  <script src="https://www.gstatic.com/firebasejs/9.6.1/firebase-auth-compat.js"></script>

  <script>
    console.log("Login page JavaScript starting...");
    alert("JavaScript is loading!"); // Immediate test

    // Add immediate form prevention
    window.addEventListener('load', function() {
      console.log("Window loaded, setting up emergency form prevention");
      const form = document.getElementById('emailLoginForm');
      if (form) {
        console.log("Found form, adding emergency prevention");
        form.onsubmit = function(e) {
          console.log("EMERGENCY: Form submission blocked!");
          e.preventDefault();
          e.stopPropagation();
          alert("Form submission blocked - JavaScript is working");
          return false;
        };
      } else {
        console.error("Form not found!");
      }
    });

    // Global variables
    let firebaseReady = false;
    let firebaseAuth = null;

    // Initialize Firebase and set up login handlers
    document.addEventListener('DOMContentLoaded', function() {
      console.log("DOM loaded, initializing Firebase...");

      // Set up form prevention first
      const emailLoginForm = document.getElementById('emailLoginForm');
      if (emailLoginForm) {
        console.log("Found email form, adding submit listener");
        emailLoginForm.addEventListener('submit', function(e) {
          console.log("Submit event triggered!");
          e.preventDefault();
          e.stopPropagation();

          if (!firebaseReady || !firebaseAuth) {
            console.log("Firebase not ready");
            alert("Please wait for Firebase to load...");
            return false;
          }

          console.log("Calling handleEmailLogin");
          handleEmailLogin();
          return false;
        });
      } else {
        console.error("Email login form not found in DOM!");
      }

      // Initialize Firebase
      fetch('/get_firebase_config')
        .then(response => {
          console.log("Firebase config response:", response);
          if (!response.ok) {
            throw new Error('Failed to fetch Firebase config');
          }
          return response.json();
        })
        .then(firebaseConfig => {
          console.log("Firebase config received:", firebaseConfig);

          // Initialize Firebase
          const app = firebase.initializeApp(firebaseConfig);
          firebaseAuth = firebase.auth();
          firebaseReady = true;

          console.log("Firebase initialized successfully");

          // Set up Google login button
          const loginBtn = document.getElementById('loginBtn');
          if (loginBtn) {
            loginBtn.addEventListener('click', handleGoogleLogin);
          }
        })
        .catch(error => {
          console.error('Error initializing Firebase:', error);
          alert('Error loading authentication system: ' + error.message);
        });
    });

    // Handle email/password login
    function handleEmailLogin() {
      console.log("Handling email login");
      const email = document.getElementById('email').value;
      const password = document.getElementById('password').value;
      const emailError = document.getElementById('emailError');

      // Clear previous errors
      emailError.textContent = '';
      emailError.style.display = 'none';

      if (!email || !password) {
        emailError.textContent = 'Please enter both email and password';
        emailError.style.display = 'block';
        return;
      }

      console.log("Attempting Firebase login with email:", email);

      firebaseAuth.signInWithEmailAndPassword(email, password)
        .then((userCredential) => {
          const user = userCredential.user;
          console.log("User signed in:", user);

          user.getIdToken().then(idToken => {
            console.log("ID token obtained");

            // Get CSRF token
            const csrfToken = document.querySelector('input[name="csrf_token"]').value;

            fetch('/set_token', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': csrfToken
              },
              body: JSON.stringify({ token: idToken }),
            })
            .then(response => {
              if (response.ok) {
                console.log("Token set successfully, redirecting...");
                window.location.href = "/";
              } else {
                console.error('Failed to set token:', response.status);
                emailError.textContent = 'Failed to authenticate. Please try again.';
                emailError.style.display = 'block';
              }
            })
            .catch(error => {
              console.error('Error setting token:', error);
              emailError.textContent = 'An error occurred. Please try again.';
              emailError.style.display = 'block';
            });
          });
        })
        .catch((error) => {
          console.error("Error signing in:", error);
          emailError.textContent = error.message;
          emailError.style.display = 'block';
        });
    }

    // Handle Google login
    function handleGoogleLogin() {
      console.log("Handling Google login");
      const googleError = document.getElementById('googleError');

      // Clear previous errors
      if (googleError) {
        googleError.textContent = '';
        googleError.style.display = 'none';
      }

      const provider = new firebase.auth.GoogleAuthProvider();

      firebaseAuth.signInWithPopup(provider)
        .then((result) => {
          if (result.credential) {
            console.log("Google user signed in:", result.user);

            result.user.getIdToken().then(idToken => {
              console.log("Google ID token obtained");

              // Get CSRF token
              const csrfToken = document.querySelector('input[name="csrf_token"]').value;

              fetch('/set_token', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                  'X-CSRFToken': csrfToken
                },
                body: JSON.stringify({ token: idToken }),
              })
              .then(response => {
                if (response.ok) {
                  console.log("Google token set successfully, redirecting...");
                  window.location.href = "/";
                } else {
                  console.error('Failed to set Google token:', response.status);
                  if (googleError) {
                    googleError.textContent = 'Failed to authenticate. Please try again.';
                    googleError.style.display = 'block';
                  }
                }
              })
              .catch(error => {
                console.error('Error setting Google token:', error);
                if (googleError) {
                  googleError.textContent = 'An error occurred. Please try again.';
                  googleError.style.display = 'block';
                }
              });
            });
          } else {
            if (googleError) {
              googleError.textContent = 'Authentication failed. Please try again.';
              googleError.style.display = 'block';
            }
          }
        })
        .catch((error) => {
          console.error("Error with Google sign in:", error);
          if (googleError) {
            googleError.textContent = error.message;
            googleError.style.display = 'block';
          }
        });
    }
  </script>
</body>
</html>