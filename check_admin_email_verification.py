#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to check the email verification status of the admin user
"""

import os
import firebase_admin
from firebase_admin import credentials, auth
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def check_admin_email_verification():
    try:
        # Initialize Firebase Admin SDK
        firebase_env_vars = {
            'type': os.environ.get('FIREBASE_ADMIN_TYPE'),
            'project_id': os.environ.get('FIREBASE_ADMIN_PROJECT_ID'),
            'private_key_id': os.environ.get('FIREBASE_ADMIN_PRIVATE_KEY_ID'),
            'private_key': os.environ.get('FIREBASE_ADMIN_PRIVATE_KEY', '').replace('\\n', '\n'),
            'client_email': os.environ.get('FIREBASE_ADMIN_CLIENT_EMAIL'),
            'client_id': os.environ.get('FIREBASE_ADMIN_CLIENT_ID'),
            'auth_uri': os.environ.get('FIREBASE_ADMIN_AUTH_URI'),
            'token_uri': os.environ.get('FIREBASE_ADMIN_TOKEN_URI'),
            'auth_provider_x509_cert_url': os.environ.get('FIREBASE_ADMIN_AUTH_PROVIDER_X509_CERT_URL'),
            'client_x509_cert_url': os.environ.get('FIREBASE_ADMIN_CLIENT_X509_CERT_URL')
        }

        # Check if all required variables are present
        if all(firebase_env_vars.values()):
            # Use environment variables
            cred = credentials.Certificate(firebase_env_vars)
        else:
            # Fall back to service account file
            service_account_path = os.environ.get('FIREBASE_SERVICE_ACCOUNT_PATH', 'instance/serviceAccountKey.json')
            cred = credentials.Certificate(service_account_path)

        firebase_admin.initialize_app(cred)
        
        # Get admin UID from environment
        admin_uid = os.environ.get('ADMIN_UIDS')
        if not admin_uid:
            print("ERROR: ADMIN_UIDS environment variable is not set")
            return
        
        # Handle multiple admin UIDs (split by comma)
        admin_uids = [uid.strip() for uid in admin_uid.split(',')]
        
        print(f"Checking email verification status for admin users...")
        print(f"Admin UIDs: {admin_uids}")
        print("-" * 50)
        
        for uid in admin_uids:
            try:
                user = auth.get_user(uid)
                print(f"UID: {uid}")
                print(f"Email: {user.email}")
                print(f"Email Verified: {user.email_verified}")
                print(f"Disabled: {user.disabled}")
                print(f"Provider Data: {[p.provider_id for p in user.provider_data]}")
                print("-" * 30)
                
                if not user.email_verified:
                    print(f"⚠️  WARNING: Admin user {user.email} has unverified email!")
                    print("This will prevent login due to email verification checks.")
                    
                    # Ask if we should verify the email
                    response = input(f"Do you want to mark {user.email} as verified? (y/n): ")
                    if response.lower() == 'y':
                        auth.update_user(uid, email_verified=True)
                        print(f"✅ Email verified for {user.email}")
                    else:
                        print("Email verification status unchanged.")
                else:
                    print(f"✅ Admin user {user.email} has verified email.")
                    
            except auth.UserNotFoundError:
                print(f"❌ User with UID {uid} not found in Firebase")
            except Exception as e:
                print(f"❌ Error checking user {uid}: {e}")
                
    except Exception as e:
        print(f"❌ Error initializing Firebase or checking users: {e}")

if __name__ == "__main__":
    check_admin_email_verification()
