#!/usr/bin/env python3
"""
Script to test the login flow by simulating the set_token request
"""

import os
import requests
import json
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_set_token_endpoint():
    """Test the /set_token endpoint with a mock token"""
    try:
        # Get CSRF token first
        csrf_response = requests.get('http://localhost:5000/get_csrf_token')
        if csrf_response.status_code != 200:
            print("❌ Failed to get CSRF token")
            return False
        
        csrf_token = csrf_response.json().get('csrf_token')
        print(f"✅ Got CSRF token: {csrf_token[:20]}...")
        
        # Test with invalid token to see the error response
        test_data = {
            'token': 'invalid_token_for_testing'
        }
        
        headers = {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrf_token
        }
        
        response = requests.post(
            'http://localhost:5000/set_token',
            headers=headers,
            json=test_data
        )
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        # We expect this to fail with 401 since it's an invalid token
        if response.status_code == 401:
            response_data = response.json()
            if 'Invalid Firebase token' in response_data.get('message', ''):
                print("✅ /set_token endpoint is working correctly (rejected invalid token)")
                return True
            elif 'Email not verified' in response_data.get('message', ''):
                print("⚠️  Token validation passed but email verification failed")
                print("This suggests the issue might be with email verification")
                return True
        
        print("❌ Unexpected response from /set_token endpoint")
        return False
        
    except Exception as e:
        print(f"❌ Error testing /set_token endpoint: {e}")
        return False

def check_login_page():
    """Check if the login page loads correctly"""
    try:
        # First, get the home page to establish a session
        session = requests.Session()
        home_response = session.get('http://localhost:5000/')
        
        if home_response.status_code != 200:
            print("❌ Home page not accessible")
            return False
        
        print("✅ Home page accessible")
        
        # Try to access login page (this might redirect due to referrer check)
        login_response = session.get(
            'http://localhost:5000/login',
            headers={'Referer': 'http://localhost:5000/'}
        )
        
        if login_response.status_code == 200:
            print("✅ Login page accessible")
            return True
        elif login_response.status_code == 302:
            print(f"⚠️  Login page redirected to: {login_response.headers.get('Location', 'unknown')}")
            return True
        else:
            print(f"❌ Login page returned status: {login_response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error checking login page: {e}")
        return False

def main():
    print("Testing Login Flow")
    print("=" * 30)
    
    # Check if app is running
    try:
        response = requests.get('http://localhost:5000/', timeout=5)
        if response.status_code != 200:
            print("❌ Flask app is not responding correctly")
            return
        print("✅ Flask app is running")
    except requests.exceptions.RequestException:
        print("❌ Flask app is not running on localhost:5000")
        return
    
    print()
    
    # Test login page access
    login_page_ok = check_login_page()
    print()
    
    # Test set_token endpoint
    set_token_ok = test_set_token_endpoint()
    print()
    
    print("Summary:")
    print(f"Login Page: {'✅' if login_page_ok else '❌'}")
    print(f"Set Token Endpoint: {'✅' if set_token_ok else '❌'}")
    
    if login_page_ok and set_token_ok:
        print("\n✅ Backend login flow appears to be working correctly.")
        print("\nThe issue is likely in the frontend JavaScript. Please:")
        print("1. Open browser developer tools (F12)")
        print("2. Go to the Console tab")
        print("3. Try to log in with your admin credentials")
        print("4. Look for any JavaScript errors or failed network requests")
        print("5. Check the Network tab for the /set_token request")
        print("\nCommon issues:")
        print("- JavaScript errors preventing form submission")
        print("- Firebase authentication failing on the client side")
        print("- CSRF token not being included in the request")
        print("- Network connectivity issues")
    else:
        print("\n❌ There are issues with the backend login flow that need to be fixed first.")

if __name__ == "__main__":
    main()
